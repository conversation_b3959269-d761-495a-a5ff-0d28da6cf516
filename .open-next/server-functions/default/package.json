{"name": "saas-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@opennextjs/aws": "^3.6.5", "@opennextjs/cloudflare": "^1.2.1", "@prisma/client": "^6.5.0", "@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.483.0", "next": "15.2.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "prisma": "^6.5.0", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.19.2"}}